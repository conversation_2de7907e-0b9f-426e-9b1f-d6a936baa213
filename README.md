# Interactive Game Experience - ZMP App

Ứng dụng game tương tác đa màn hình với tính năng tải lên hình ảnh, khảo sát và video thưởng, được xây dựng trên nền tảng Zalo Mini Program (ZMP).

## 🎮 Tính năng chính

- **4 màn hình game hoàn chỉnh**: <PERSON><PERSON><PERSON> mừ<PERSON> → <PERSON><PERSON><PERSON> ảnh → Khảo sát → Kết quả
- **Tải lên hình ảnh**: Hỗ trợ JPG, PNG, GIF, WebP (tối đa 10MB/file, 5 files)
- **Hệ thống khảo sát**: Nhiều loại câu hỏi (trắc nghiệm, text, rating, slider, checkbox)
- **Video thưởng**: Phát video kết quả cho người dùng
- **Responsive design**: Tối ưu cho mọi kích thước màn hình
- **Lưu trữ local**: Không cần backend, dữ liệu lưu trong localStorage
- **Giao diện tiếng Việt**: Hoàn toàn bằng tiếng Việt

## 🏗️ Cấu trúc dự án

```
src/
├── components/
│   └── game/
│       ├── GameApp.tsx          # Component chính quản lý game
│       ├── WelcomeScreen.tsx    # Màn hình chào mừng
│       ├── ImageUploadScreen.tsx # Màn hình tải ảnh
│       ├── SurveyScreen.tsx     # Màn hình khảo sát
│       └── ResultsScreen.tsx    # Màn hình kết quả
├── utils/
│   ├── gameUtils.ts             # Utilities cho game logic
│   └── api.ts                   # Mock API functions
├── css/
│   └── game.scss               # Styles cho game
└── pages/
    └── index.tsx               # Trang chủ với link vào game
```

## 🚀 Development

### Using Zalo Mini App Extension

1. Install [Visual Studio Code](https://code.visualstudio.com/download) and [Zalo Mini App Extension](https://mini.zalo.me/docs/dev-tools).
1. In the **Home** tab, process **Config App ID** and **Install Dependencies**.
1. Navigate to the **Run** tab, select the suitable launcher, and click **Start**.

### Using Zalo Mini App CLI

1. [Install Node JS](https://nodejs.org/en/download/).
1. [Install Zalo Mini App CLI](https://mini.zalo.me/docs/dev-tools/cli/intro/).
1. **Install dependencies**:
   ```bash
   npm install
   ```
1. **Start** the dev server:
   ```bash
   zmp start
   ```
1. **Open** `localhost:3000` in your browser.

## Deployment

1. **Create** a mini program. For instructions on how to create a mini program, please refer to the [Coffee Shop Tutorial](https://mini.zalo.me/tutorial/coffee-shop/step-1/)

1. **Deploy** your mini program to Zalo using the mini app ID created.

   - **Using Zalo Mini App Extension**: navigate to the **Deploy** panel > **Login** > **Deploy**.
   - **Using Zalo Mini App CLI**:
     ```bash
     zmp login
     zmp deploy
     ```

1. Open the mini app in Zalo by scanning the QR code.

## 🎯 Cách sử dụng

1. **Màn hình chào mừng**: Người dùng xem giới thiệu và bấm "Bắt đầu hành trình"
2. **Tải lên hình ảnh**: Chọn và tải lên tối đa 5 hình ảnh
3. **Trả lời khảo sát**: Hoàn thành 6 câu hỏi với các định dạng khác nhau
4. **Xem kết quả**: Nhận điểm số, danh mục và video thưởng

## 🛠️ Tùy chỉnh

### Thêm câu hỏi khảo sát mới

Chỉnh sửa file `src/utils/gameUtils.ts`, function `getSampleQuestions()`:

```typescript
{
  id: 'q7',
  type: 'multiple-choice',
  question: 'Câu hỏi mới của bạn?',
  options: ['Lựa chọn 1', 'Lựa chọn 2', 'Lựa chọn 3'],
  required: true
}
```

### Thay đổi logic tính điểm

Chỉnh sửa function `calculateGameResults()` trong `src/utils/gameUtils.ts`

### Tùy chỉnh giao diện

Chỉnh sửa file `src/css/game.scss` để thay đổi màu sắc, font chữ, layout

## 📱 Responsive Design

- **Mobile First**: Thiết kế ưu tiên mobile
- **Tablet Support**: Tối ưu cho màn hình tablet
- **Desktop Compatible**: Hoạt động tốt trên desktop

## 🔧 Technical Stack

- **Frontend**: React + TypeScript
- **UI Framework**: ZMP UI (Zalo Mini Program UI)
- **Styling**: SCSS với CSS Variables
- **State Management**: React Hooks + localStorage
- **Build Tool**: Vite
- **No Backend Required**: Tất cả logic chạy client-side

## Resources

- [Zalo Mini App Official Website](https://mini.zalo.me/)
- [ZaUI Documentation](https://mini.zalo.me/documents/zaui/)
- [ZMP SDK Documentation](https://mini.zalo.me/documents/api/)
- [DevTools Documentation](https://mini.zalo.me/docs/dev-tools/)
- [Ready-made Mini App Templates](https://mini.zalo.me/zaui-templates)
- [Community Support](https://mini.zalo.me/community)
