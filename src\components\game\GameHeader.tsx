import React from 'react';
import { Box } from 'zmp-ui';

/**
 * GameHeader component - A consistent header for all game screens
 * 
 * Specifications:
 * - Height: exactly 48px
 * - Background color: #0c1bac (dark blue)
 * - Positioned at the top of each screen
 * - Fixed positioning to stay at top during scroll
 * - z-index above global progress bar
 */
const GameHeader: React.FC = () => {
  return (
    <Box
      className="game-header"
      style={{
        height: '48px',
        backgroundColor: '#0c1bac',
        width: '100%',
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        zIndex: 1001, // Above the global progress bar (z-index: 1000)
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)' // Subtle shadow for depth
      }}
    >
      {/* Header content can be added here if needed in the future */}
      {/* For now, keeping it empty as per requirements */}
    </Box>
  );
};

export default GameHeader;
