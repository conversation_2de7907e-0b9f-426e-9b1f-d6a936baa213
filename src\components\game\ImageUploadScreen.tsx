import React, { useState, useRef } from 'react';
import { Button, Box, Text, Page, Icon, useNavigate } from 'zmp-ui';
import bgImage from '@/static/bg.png';
import selectZoneImage from '@/static/select-zone.png';
import GameHeader from './GameHeader';

interface ImageUploadScreenProps {
  onImageUpload: (images: File[]) => void;
  uploadedImages: File[];
}

const ImageUploadScreen: React.FC<ImageUploadScreenProps> = ({
  onImageUpload,
  uploadedImages
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);

  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 5;

  const validateFile = (file: File): string | null => {
    if (!supportedFormats.includes(file.type)) {
      return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
    }
    if (file.size > maxFileSize) {
      return 'File size too large. Maximum size is 10MB.';
    }
    return null;
  };

  const handleFiles = async (files: FileList) => {
    setUploading(true);
    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      // Check if file is a valid File object
      if (!file || !(file instanceof File)) {
        errors.push(`Invalid file object`);
        return;
      }

      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else if (validFiles.length + uploadedImages.length < maxFiles) {
        validFiles.push(file);
      } else {
        errors.push(`Maximum ${maxFiles} files allowed`);
      }
    });

    if (errors.length > 0) {
      // Show error toast or modal
      console.error('Upload errors:', errors);
    }

    if (validFiles.length > 0) {
      // Filter out any existing invalid files and add new valid files
      const currentValidFiles = uploadedImages.filter(file => file && file instanceof File);
      onImageUpload([...currentValidFiles, ...validFiles]);
    }

    setUploading(false);
  };

  const handleFileSelect = () => {
    fileInputRef.current?.click();
  };

  const handleDrag = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(e.dataTransfer.files);
    }
  };

  const removeImage = (index: number) => {
    // Filter out invalid files and remove the specified index
    const validFiles = uploadedImages.filter(file => file && file instanceof File);
    const newImages = validFiles.filter((_, i) => i !== index);
    onImageUpload(newImages);
  };

  const handleNext = () => {
    if (uploadedImages.length > 0) {
      navigate('/survey');
    }
  };

  return (
    <Page
      className="image-upload-screen"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Header */}
      <GameHeader />

      {/* Progress Indicator */}
      <Box className="progress-indicator">
        <div className="progress-step completed">1</div>
        <div className="progress-step active">2</div>
        <div className="progress-step">3</div>
        <div className="progress-step">4</div>
      </Box>

      {/* Main Content Area */}
      <Box className="main-content" style={{ padding: '20px' }}>
        {/* Upload Area */}
        <Box
          className={`upload-area ${dragActive ? 'drag-active' : ''}`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <div className="upload-icon">📸</div>
          <Text.Title>Drop your images here</Text.Title>
          <Text>or click to browse</Text>

          <Button
            variant="primary"
            onClick={handleFileSelect}
            disabled={uploading}
          >
            {uploading ? 'Uploading...' : 'Choose Files'}
          </Button>

          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={supportedFormats.join(',')}
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
            style={{ display: 'none' }}
          />
        </Box>

        {/* File Info */}
        <Box className="file-info">
          <Text>Supported formats: JPG, PNG, GIF, WebP</Text>
          <Text>Maximum file size: 10MB</Text>
          <Text>Maximum files: {maxFiles}</Text>
        </Box>

        {/* Uploaded Images Preview */}
        {uploadedImages.length > 0 && (
          <Box className="uploaded-images">
            <Text.Title>Uploaded Images ({uploadedImages.length})</Text.Title>
            <Box className="image-grid">
              {uploadedImages
                .filter(file => file && file instanceof File) // Filter out invalid files
                .map((file, index) => {
                  let imageUrl: string;
                  try {
                    imageUrl = URL.createObjectURL(file);
                  } catch (error) {
                    console.error('Error creating object URL for file:', file, error);
                    return null; // Skip this file if URL creation fails
                  }

                  return (
                    <Box key={index} className="image-preview">
                      <img
                        src={imageUrl}
                        alt={`Upload ${index + 1}`}
                        className="preview-image"
                        onLoad={() => {
                          // Clean up the object URL after the image loads to free memory
                          URL.revokeObjectURL(imageUrl);
                        }}
                        onError={() => {
                          console.error('Error loading image:', file.name);
                          URL.revokeObjectURL(imageUrl);
                        }}
                      />
                      <Button
                        variant="tertiary"
                        size="small"
                        onClick={() => removeImage(index)}
                        className="remove-button"
                      >
                        <Icon icon="zi-close" />
                      </Button>
                      <Text className="file-name">{file.name}</Text>
                    </Box>
                  );
                })
                .filter(Boolean) // Remove null entries
              }
            </Box>
          </Box>
        )}
      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box
        className="selected-zone"
        style={{
          backgroundImage: `url(${selectZoneImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <Box className="action-buttons">
          <Button
            variant="primary"
            size="large"
            fullWidth
            onClick={handleNext}
            disabled={uploadedImages.length === 0}
            className="continue-button"
          >
            Tiếp tục
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default ImageUploadScreen;
