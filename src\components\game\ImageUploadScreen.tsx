import React, { useState, useRef } from 'react';
import { Box, Page, useNavigate } from 'zmp-ui';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import uploadPicImage from '@/static/upload/upload-pic.png';
import flowerImage from '@/static/flower.png';
import { trackScreenView, trackButtonClick, trackImageUpload, trackError } from '@/utils/ga4-tracking';

interface ImageUploadScreenProps {
  onImageUpload: (images: File[]) => void;
  uploadedImages: File[];
}

const ImageUploadScreen: React.FC<ImageUploadScreenProps> = ({
  onImageUpload,
  uploadedImages
}) => {
  const navigate = useNavigate();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [userName, setUserName] = useState('');
  const [uploading, setUploading] = useState(false);

  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('ImageUploadScreen', 'Game');
  }, []);

  const supportedFormats = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB
  const maxFiles = 5;

  const validateFile = (file: File): string | null => {
    if (!supportedFormats.includes(file.type)) {
      return 'Unsupported file format. Please use JPG, PNG, GIF, or WebP.';
    }
    if (file.size > maxFileSize) {
      return 'File size too large. Maximum size is 10MB.';
    }
    return null;
  };

  const handleFiles = async (files: FileList) => {
    setUploading(true);
    const validFiles: File[] = [];
    const errors: string[] = [];

    Array.from(files).forEach(file => {
      // Check if file is a valid File object
      if (!file || !(file instanceof File)) {
        errors.push(`Invalid file object`);
        return;
      }

      const error = validateFile(file);
      if (error) {
        errors.push(`${file.name}: ${error}`);
      } else if (validFiles.length + uploadedImages.length < maxFiles) {
        validFiles.push(file);
      } else {
        errors.push(`Maximum ${maxFiles} files allowed`);
      }
    });

    if (errors.length > 0) {
      // Show error toast or modal
      console.error('Upload errors:', errors);
      // Track upload errors
      await trackError('image_upload_error', errors.join('; '), 'ImageUploadScreen');
    }

    if (validFiles.length > 0) {
      // Filter out any existing invalid files and add new valid files
      const currentValidFiles = uploadedImages.filter(file => file && file instanceof File);
      const updatedImages = [...currentValidFiles, ...validFiles];
      onImageUpload(updatedImages);
      
      // Track successful image upload
      const sessionId = localStorage.getItem('gameState') ? 
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId : 
        'unknown';
      const totalSize = updatedImages.reduce((sum, file) => sum + file.size, 0);
      await trackImageUpload(sessionId, updatedImages.length, totalSize);
    }

    setUploading(false);
  };

  const handleFileSelect = async () => {
    await trackButtonClick('select_image', 'ImageUploadScreen', {
      button_location: 'upload_area',
      current_image_count: uploadedImages.length
    });
    fileInputRef.current?.click();
  };

  const handleNext = async () => {
    if (uploadedImages.length > 0) {
      await trackButtonClick('next', 'ImageUploadScreen', {
        button_location: 'footer',
        image_count: uploadedImages.length,
        user_name: userName || 'not_provided'
      });
      navigate('/survey');
    }
  };

  return (
    <Page className="image-upload-screen">
      {/* Header */}
      <GameHeader />
    
      {/* Main Content Area */}
      <Box className="main-content">

        <Box className='hero-section'>
                    {/* Header Section with Title and Name Input */}
        <Box className="upload-header-section">
          <h2 className="gradient-text upload-title">
            Phiếu Bé Ngoan này của:
          </h2>
          
          <label className="name-input-label">
            Nhập tên của bạn
          </label>
          
          <Box className="name-input-container">
            <span 
              className="dotted-placeholder"
              style={{ opacity: userName ? 0 : 1 }}
            >
              ..........................................................................
            </span>
            <input
              type="text"
              value={userName}
              onChange={(e) => setUserName(e.target.value)}
              className="name-input"
            />
          </Box>
        </Box>

        <img src={flowerImage} alt="Flower" className="flower-image" />


        {/* Photo Section with Frame and Upload Area */}
        <Box className="photo-upload-section">
          <Box 
            onClick={handleFileSelect}
            className="upload-frame-container"
          >
            <img 
              src={uploadPicImage} 
              alt="Upload frame"
              className="upload-frame"
            />
            
            {/* Display uploaded image */}
            {uploadedImages.length > 0 && uploadedImages[0] && (
              <Box className="uploaded-image-container">
                <img
                  src={URL.createObjectURL(uploadedImages[0])}
                  alt="Uploaded"
                  className="uploaded-image"
                  onLoad={(e) => {
                    // Clean up the object URL after the image loads
                    URL.revokeObjectURL(e.currentTarget.src);
                  }}
                />
              </Box>
            )}
          </Box>
          
          <input
            ref={fileInputRef}
            type="file"
            multiple
            accept={supportedFormats.join(',')}
            onChange={(e) => e.target.files && handleFiles(e.target.files)}
          />
        </Box>

        {/* Instructions Section */}
        <Box className="instructions-section">
          <p className="gradient-text instructions-text">
            Tải hình ảnh của bạn<br />
            chụp với Cha Mẹ
          </p>
        </Box>
        </Box>

      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <button 
            className="image-button" 
            onClick={handleNext}
            disabled={uploadedImages.length === 0}
          >
            <img src={selectButtonImage} alt="Continue" />
            <span className="button-text gradient-text">TIẾP TỤC</span>
          </button>
        </Box>
      </Box>
    </Page>
  );
};

export default ImageUploadScreen;
