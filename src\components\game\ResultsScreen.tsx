import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, Box, Text, Page, Icon, useNavigate } from 'zmp-ui';
import bgImage from '@/static/bg.png';
import selectZoneImage from '@/static/select-zone.png';
import GameHeader from './GameHeader';

interface GameResults {
  score: number;
  category: string;
  insights: string[];
  videoUrl: string;
  shareableImage?: string;
}

interface ResultsScreenProps {
  uploadedImages: File[];
  surveyResponses: any[];
  onRestart: () => void;
}

const ResultsScreen: React.FC<ResultsScreenProps> = ({
  uploadedImages,
  surveyResponses,
  onRestart
}) => {
  const navigate = useNavigate();
  const [results, setResults] = useState<GameResults | null>(null);
  const [loading, setLoading] = useState(true);
  const [videoPlaying, setVideoPlaying] = useState(false);

  useEffect(() => {
    // Simulate processing results
    const processResults = async () => {
      setLoading(true);

      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Generate mock results based on inputs
      const mockResults: GameResults = {
        score: Math.floor(Math.random() * 100) + 1,
        category: getResultCategory(),
        insights: generateInsights(),
        videoUrl: '/static/reward-video.mp4',
        shareableImage: generateShareableImage()
      };

      setResults(mockResults);
      setLoading(false);
    };

    processResults();
  }, [uploadedImages, surveyResponses]);

  const getResultCategory = (): string => {
    const categories = [
      'Người con hiếu thảo',
      'Trụ cột gia đình',
      'Tấm lòng vàng',
      'Người bạn đồng hành',
      'Nguồn cảm hứng yêu thương'
    ];
    return categories[Math.floor(Math.random() * categories.length)];
  };

  const generateInsights = (): string[] => {
    const insights = [
      'Bạn là người luôn biết trân trọng và ghi nhận những nỗ lực của bản thân.',
      'Gia đình là nguồn động lực mạnh mẽ nhất trong cuộc sống của bạn.',
      'Bạn thể hiện tình yêu thương qua những hành động cụ thể, thiết thực.',
      'Bạn hiểu rằng hạnh phúc đích thực đến từ những khoảnh khắc bên người thân.',
      'Bạn có khả năng cân bằng tốt giữa việc chăm sóc bản thân và gia đình.',
      'Bạn là người biết lắng nghe và chia sẻ với những người xung quanh.',
      'Bạn có tầm nhìn tích cực và luôn tin tưởng vào tương lai tươi sáng.',
      'Bạn thể hiện sự trưởng thành qua cách quan tâm đến sức khỏe của ba mẹ.'
    ];

    // Return 3 random insights
    return insights.sort(() => 0.5 - Math.random()).slice(0, 3);
  };

  const generateShareableImage = (): string => {
    // In a real app, this would generate a custom image with results
    return '/static/shareable-result.png';
  };

  const handleVideoPlay = () => {
    setVideoPlaying(true);
  };

  const handleShare = async () => {
    if (navigator.share && results) {
      try {
        await navigator.share({
          title: 'My Game Results',
          text: `I scored ${results.score} points and got "${results.category}"!`,
          url: window.location.href
        });
      } catch (error) {
        console.log('Error sharing:', error);
        // Fallback to copy to clipboard
        copyToClipboard();
      }
    } else {
      copyToClipboard();
    }
  };

  const copyToClipboard = () => {
    if (results) {
      const shareText = `I scored ${results.score} points and got "${results.category}"! Check out this amazing game!`;
      navigator.clipboard.writeText(shareText);
      // Show toast notification
    }
  };

  const handleRestart = () => {
    onRestart();
    navigate('/');
  };

  if (loading) {
    return (
      <Page
        className="results-screen loading"
        style={{
          backgroundImage: `url(${bgImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          backgroundAttachment: 'fixed'
        }}
      >
        <Box
          className="selected-zone loading-container"
          style={{
            backgroundImage: `url(${selectZoneImage})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center',
            backgroundRepeat: 'no-repeat'
          }}
        >
          <div className="loading-spinner"></div>
          <Text.Title>Đang xử lý kết quả...</Text.Title>
          <Text>Phân tích hình ảnh và câu trả lời khảo sát của bạn</Text>
          <Box className="loading-steps">
            <div className="loading-step">✓ Đã xử lý hình ảnh</div>
            <div className="loading-step">✓ Đã phân tích khảo sát</div>
            <div className="loading-step active">⏳ Đang tạo thông tin chi tiết</div>
          </Box>
        </Box>
      </Page>
    );
  }

  if (!results) {
    return (
      <Page className="results-screen error">
        <Box className="error-container">
          <Text.Title>Something went wrong</Text.Title>
          <Text>We couldn't process your results. Please try again.</Text>
          <Button variant="primary" onClick={handleRestart}>
            Start Over
          </Button>
        </Box>
      </Page>
    );
  }

  return (
    <Page
      className="results-screen"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Header */}
      <GameHeader />

      {/* Progress Indicator */}
      <Box className="progress-indicator">
        <div className="progress-step completed">1</div>
        <div className="progress-step completed">2</div>
        <div className="progress-step completed">3</div>
        <div className="progress-step active">4</div>
      </Box>

      {/* Main Content Area */}
      <Box className="main-content" style={{ padding: '20px' }}>

        {/* Results Summary */}
        <Box className="results-summary">
          <Box className="score-display">
            <Text className="score-label">Your Score</Text>
            <Text className="score-value">{results.score}</Text>
            <Text className="score-max">/ 100</Text>
          </Box>

          <Box className="category-display">
            <Text.Title className="category-title">
              You are a {results.category}!
            </Text.Title>
          </Box>
        </Box>

        {/* Insights */}
        <Box className="insights-section">
          <Text.Title>Your Insights</Text.Title>
          <Box className="insights-list">
            {results.insights.map((insight, index) => (
              <Box key={index} className="insight-item">
                <div className="insight-icon">💡</div>
                <Text className="insight-text">{insight}</Text>
              </Box>
            ))}
          </Box>
        </Box>

        {/* Image Gallery */}
        <Box className="image-gallery">
          <Text.Title>Your Uploaded Images</Text.Title>
          <Box className="gallery-grid">
            {uploadedImages.slice(0, 4).map((image, index) => (
              <Box key={index} className="gallery-item">
                <img
                  src={URL.createObjectURL(image)}
                  alt={`Upload ${index + 1}`}
                  className="gallery-image"
                />
              </Box>
            ))}
          </Box>
        </Box>

        {/* Reward Video */}
        <Box className="video-section">
          <Text.Title>Your Reward Video</Text.Title>
          <Box className="video-container">
            {!videoPlaying ? (
              <Box className="video-placeholder" onClick={handleVideoPlay}>
                <div className="play-button">▶️</div>
                <Text>Click to watch your reward video</Text>
              </Box>
            ) : (
              <video
                controls
                autoPlay
                className="reward-video"
                poster="/static/video-poster.jpg"
              >
                <source src={results.videoUrl} type="video/mp4" />
                Your browser does not support the video tag.
              </video>
            )}
          </Box>
        </Box>

        {/* Statistics */}
        <Box className="statistics">
          <Text.Title>Thống kê Game</Text.Title>
          <Box className="stats-grid">
            <Box className="stat-item">
              <Text className="stat-value">{uploadedImages.length}</Text>
              <Text className="stat-label">Ảnh đã tải</Text>
            </Box>
            <Box className="stat-item">
              <Text className="stat-value">{surveyResponses.length}</Text>
              <Text className="stat-label">Câu hỏi đã trả lời</Text>
            </Box>
            <Box className="stat-item">
              <Text className="stat-value">100%</Text>
              <Text className="stat-label">Tỷ lệ hoàn thành</Text>
            </Box>
          </Box>
        </Box>

      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box
        className="selected-zone"
        style={{
          backgroundImage: `url(${selectZoneImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <Box className="action-buttons">
          <Box className="navigation-buttons">
            <Button
              variant="primary"
              onClick={handleRestart}
              className="restart-button"
            >
              Chơi lại
            </Button>

            <Button
              variant="secondary"
              onClick={handleShare}
              className="share-button"
            >
              <Icon icon="zi-share" />
              Chia sẻ
            </Button>
          </Box>
        </Box>
      </Box>
    </Page>
  );
};

export default ResultsScreen;
