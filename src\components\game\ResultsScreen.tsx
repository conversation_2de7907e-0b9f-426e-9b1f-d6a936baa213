import React from 'react';
import { Box, Page, Text } from 'zmp-ui';
import { openShareSheet } from 'zmp-sdk';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import heroImage from '@/static/welcome/hero.png';
import flowerImage from '@/static/flower.png';
import prizesImage from '@/static/prizes.png';
import { shareResults } from '@/utils/gameUtils';
import { trackScreenView, trackButtonClick, trackShare, trackGameRestart, trackGameComplete } from '@/utils/ga4-tracking';

interface ResultsScreenProps {
  uploadedImages: File[];
  surveyResponses: any[];
  userData?: {
    name?: string;
    avatar?: string;
    idByOA?: string | null;
    mobile?: string | null;
    videoUrl?: string;
    apiMessage?: string;
  };
  videoUrl?: string;
  onRestart: () => void;
}

const ResultsScreen: React.FC<ResultsScreenProps> = ({
  uploadedImages,
  surveyResponses,
  userData,
  videoUrl,
  onRestart
}) => {
  React.useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('ResultsScreen', 'Game');

    // Track game completion
    const sessionId = localStorage.getItem('gameState') ?
      JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
      'unknown';
    const gameState = JSON.parse(localStorage.getItem('gameState') || '{}');
    const startTime = gameState.startTime ? new Date(gameState.startTime).getTime() : Date.now();
    const completionTime = Math.floor((Date.now() - startTime) / 1000);

    // Use default score value of 1
    const score = 1;
    const category = 'Người con hiếu thảo';

    trackGameComplete(sessionId, score, category, completionTime);
  }, []);

  const handleShare = async () => {
    try {
      // Track share button click
      await trackButtonClick('share', 'ResultsScreen', {
        button_location: 'footer',
        button_text: 'CHIA SẺ'
      });

      // Use the actual video URL if available, otherwise fall back to placeholder
      const shareVideoUrl = videoUrl || userData?.videoUrl || "https://www.w3schools.com/html/mov_bbb.mp4";

      const data = await openShareSheet({
        type: "link",
        data: {
          link: shareVideoUrl,
          title: "Phiếu Bé Ngoan của tôi",
          description: userData?.apiMessage || "Xem video Phiếu Bé Ngoan của tôi!"
        },
      });
      console.log(data);

      // Track successful share
      const sessionId = localStorage.getItem('gameState') ?
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
        'unknown';
      await trackShare(sessionId, 'link', 'zalo_share_sheet');
    } catch (error) {
      console.error('Error sharing video:', error);
    }
  };

  return (
    <Page className="results-screen">
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content results-main-content">
        {/* Hero Section */}
        <Box className="hero-section">
          {/* Video Section - 9:16 aspect ratio */}
          <Box className="video-section video-wrapper">
            {(videoUrl || userData?.videoUrl) ? (
              <iframe
                src={videoUrl || userData?.videoUrl}
                className="video-iframe"
                style={{
                  width: '100%',
                  height: '100%',
                  border: 'none',
                  borderRadius: '8px'
                }}
                title="Personalized Video Result"
                allow="autoplay; fullscreen"
                allowFullScreen
              />
            ) : (
              <Box className="video-placeholder-container">
                <Text className="video-placeholder-text">Video Placeholder</Text>
              </Box>
            )}
          </Box>

          {/* Text Content Section */}
          <Box className="text-content">
            <Text className="gradient-text share-heading">
              Hãy chia sẻ phiếu bé ngoan cường
            </Text>

            <Text className="gradient-text share-subheading">
              để có cơ hội trúng nhiều quà tặng hấp dẫn
            </Text>
          </Box>

          <Box className="prizes-section">
            <img src={prizesImage} alt="Prizes" className="prizes-image" />
          </Box>
        </Box>
      </Box>

      {/* Footer Section */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <Box className="navigation-buttons">
            <button className="image-button" onClick={async () => {
              // Track restart button click
              await trackButtonClick('restart_game', 'ResultsScreen', {
                button_location: 'footer',
                button_text: 'CHƠI LẠI'
              });

              // Track game restart with old and new session IDs
              const oldSessionId = localStorage.getItem('gameState') ?
                JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
                'unknown';
              const newSessionId = 'game_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
              await trackGameRestart(oldSessionId, newSessionId);

              onRestart();
            }}>
              <img src={selectButtonImage} alt="Chơi lại" />
              <span className="button-text gradient-text">CHƠI LẠI</span>
            </button>

            <button className="image-button" onClick={handleShare}>
              <img src={selectButtonImage} alt="Chia sẻ" />
              <span className="button-text gradient-text">CHIA SẺ</span>
            </button>
          </Box>
        </Box>
      </Box>
    </Page>
  );
};

export default ResultsScreen;