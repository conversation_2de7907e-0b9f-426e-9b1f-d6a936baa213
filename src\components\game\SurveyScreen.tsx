import React, { useState } from 'react';
import { Button, Box, Text, Page, Icon, Input, Radio, Checkbox, Slider, useNavigate } from 'zmp-ui';
import bgImage from '@/static/bg.png';
import selectZoneImage from '@/static/select-zone.png';
import GameHeader from './GameHeader';

interface Question {
  id: string;
  type: 'multiple-choice' | 'text' | 'rating' | 'yes-no' | 'checkbox' | 'slider';
  question: string;
  options?: string[];
  required: boolean;
  min?: number;
  max?: number;
}

interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
}

interface SurveyScreenProps {
  onSurveyComplete: (responses: SurveyResponse[]) => void;
}

const SurveyScreen: React.FC<SurveyScreenProps> = ({ onSurveyComplete }) => {
  const navigate = useNavigate();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [currentAnswer, setCurrentAnswer] = useState<string | string[] | number>('');

  // Survey questions về chủ đề gia đình và mùa Vu Lan
  const questions: Question[] = [
    {
      id: 'q1',
      type: 'multiple-choice',
      question: 'Thời gian qua, bạn đã "vượt" chính mình như thế nào?',
      options: [
        'Biến "chưa biết" thành một chứng chỉ/bằng cấp đàng hoàng nè',
        'Dọn đến nơi ở mới – hoặc biến nhà thành tổ ấm thật sự',
        'Hoàn thành công việc/dự án mà bạn từng nghĩ "chắc mình sẽ không làm được đâu"',
        'Học cách "lắng nghe cơ thể mình" và chăm sóc đúng cách',
        'Vừa chăm được mình, vừa lo chu toàn cho sức khỏe ba mẹ'
      ],
      required: true
    },
    {
      id: 'q2',
      type: 'multiple-choice',
      question: 'Trước những bất định trong cuộc sống, đâu là động lực khiến bạn tiếp tục cố gắng?',
      options: [
        'Nhớ lại lý do mình bắt đầu',
        'Tự nhủ: "Rồi sẽ ổn thôi mà"',
        'Nghỉ ngơi để tiếp thêm năng lượng',
        'Nghĩ đến gia đình, Cha Mẹ – điểm tựa vững chắc cho tôi',
        'Tìm kiếm sự sẻ chia từ bạn bè và những người xung quanh'
      ],
      required: true
    },
    {
      id: 'q3',
      type: 'multiple-choice',
      question: 'Trên hành trình nỗ lực và cố gắng ấy, gia đình với bạn là…?',
      options: [
        'Điểm tựa vững vàng tiếp thêm sức mạnh cho tôi',
        'Cái ô che chở tôi giữa những mưa cuộc đời',
        'Vòng tay rộng mở đón tôi trở về trong mọi hoàn cảnh',
        'Lời động viên truyền động lực',
        'Tấm gương sáng truyền cảm hứng để cho tôi thấy thế nào là sống tử tế và kiên cường'
      ],
      required: true
    },
    {
      id: 'q4',
      type: 'multiple-choice',
      question: 'Thay vì nói "con thương ba mẹ", bạn thường thể hiện sự quan tâm đến Cha Mẹ bằng cách nào?',
      options: [
        'Hỏi thăm sức khỏe thường xuyên, không chỉ "lúc rảnh"',
        'Dẫn ba mẹ đi khám định kỳ, không đợi khi ốm mới đi',
        'Vào bếp tự tay nấu bữa cơm gia đình cho ba mẹ',
        'Lắng nghe và dành thời gian trò chuyện với Cha Mẹ',
        'Nhắc ba mẹ uống sữa mỗi ngày, như cách họ từng chăm tôi khi bé'
      ],
      required: true
    },
    {
      id: 'q5',
      type: 'multiple-choice',
      question: 'Từ mùa Vu Lan này, bạn muốn cùng ba mẹ tạo nên những khoảnh khắc nào?',
      options: [
        'Cùng đi xem bộ phim ba mẹ yêu thích',
        'Cùng dự một đêm nhạc gợi nhớ thanh xuân',
        'Cùng kể chuyện cũ, nghe ba mẹ nói "ngày xưa…"',
        'Cùng nấu một bữa cơm đàng vị nhà',
        'Cùng hát lại bản karaoke "hồi sáu tồng mà vui"',
        'Cùng tập thể dục – mỗi người một động tác, nhưng cùng nhịp',
        'Cùng đi du lịch – có thể gần thôi, nhưng là cùng nhau',
        'Cùng ngồi quán café, nhìn phố phường trôi chầm chậm'
      ],
      required: true
    }
  ];

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  const handleAnswerChange = (answer: string | string[] | number) => {
    setCurrentAnswer(answer);
  };

  const handleNext = () => {
    // Save current answer
    const newResponse: SurveyResponse = {
      questionId: currentQuestion.id,
      answer: currentAnswer
    };

    const updatedResponses = responses.filter(r => r.questionId !== currentQuestion.id);
    updatedResponses.push(newResponse);
    setResponses(updatedResponses);

    if (isLastQuestion) {
      // Complete survey
      onSurveyComplete(updatedResponses);
      navigate('/results');
    } else {
      // Move to next question
      setCurrentQuestionIndex(currentQuestionIndex + 1);
      // Load existing answer if available
      const existingResponse = updatedResponses.find(r => r.questionId === questions[currentQuestionIndex + 1].id);
      setCurrentAnswer(existingResponse?.answer || '');
    }
  };

  const isAnswerValid = () => {
    if (!currentQuestion.required) return true;

    switch (currentQuestion.type) {
      case 'text':
        return typeof currentAnswer === 'string' && currentAnswer.trim().length > 0;
      case 'checkbox':
        return Array.isArray(currentAnswer) && currentAnswer.length > 0;
      case 'slider':
      case 'rating':
        return typeof currentAnswer === 'number' && currentAnswer > 0;
      default:
        return currentAnswer !== '';
    }
  };

  const renderQuestion = () => {
    switch (currentQuestion.type) {
      case 'multiple-choice':
        return (
          <Radio.Group
            value={currentAnswer as string}
            onChange={handleAnswerChange}
          >
            {currentQuestion.options?.map((option, index) => (
              <Radio key={index} value={option} label={option} />
            ))}
          </Radio.Group>
        );

      case 'checkbox':
        return (
          <Checkbox.Group
            value={currentAnswer as string[]}
            onChange={handleAnswerChange}
          >
            {currentQuestion.options?.map((option, index) => (
              <Checkbox key={index} value={option} label={option} />
            ))}
          </Checkbox.Group>
        );

      case 'text':
        return (
          <Input.TextArea
            value={currentAnswer as string}
            onChange={(e) => handleAnswerChange(e.target.value)}
            placeholder="Type your answer here..."
            rows={4}
          />
        );

      case 'rating':
        return (
          <Box className="rating-container">
            <Box className="rating-buttons">
              {Array.from({ length: currentQuestion.max || 5 }, (_, i) => i + 1).map(rating => (
                <Button
                  key={rating}
                  variant={currentAnswer === rating ? 'primary' : 'secondary'}
                  onClick={() => handleAnswerChange(rating)}
                  className="rating-button"
                >
                  {rating}
                </Button>
              ))}
            </Box>
            <Box className="rating-labels">
              <Text>Poor</Text>
              <Text>Excellent</Text>
            </Box>
          </Box>
        );

      case 'yes-no':
        return (
          <Radio.Group
            value={currentAnswer as string}
            onChange={handleAnswerChange}
          >
            <Radio value="yes" label="Yes" />
            <Radio value="no" label="No" />
          </Radio.Group>
        );

      case 'slider':
        return (
          <Box className="slider-container">
            <Slider
              value={currentAnswer as number}
              onChange={handleAnswerChange}
              min={currentQuestion.min || 0}
              max={currentQuestion.max || 10}
              step={1}
            />
            <Box className="slider-labels">
              <Text>{currentQuestion.min || 0}</Text>
              <Text className="slider-value">{currentAnswer}</Text>
              <Text>{currentQuestion.max || 10}</Text>
            </Box>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Page
      className="survey-screen"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed'
      }}
    >
      {/* Header */}
      <GameHeader />

      {/* Progress Indicator */}
      <Box className="progress-indicator">
        <div className="progress-step completed">1</div>
        <div className="progress-step completed">2</div>
        <div className="progress-step active">3</div>
        <div className="progress-step">4</div>
      </Box>

      {/* Main Content Area */}
      <Box className="main-content" style={{ padding: '20px' }}>
        {/* Question Progress */}
        <Box className="question-progress">
          <Text>Câu hỏi {currentQuestionIndex + 1} / {questions.length}</Text>
          <Box className="progress-bar">
            <div
              className="progress-fill"
              style={{ width: `${((currentQuestionIndex + 1) / questions.length) * 100}%` }}
            />
          </Box>
        </Box>

        {/* Question */}
        <Box className="question-container">
          <Text.Title className="question-text">
            {currentQuestion.question}
            {currentQuestion.required && <span className="required">*</span>}
          </Text.Title>

          <Box className="answer-container">
            {renderQuestion()}
          </Box>
        </Box>
      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box
        className="selected-zone"
        style={{
          backgroundImage: `url(${selectZoneImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        }}
      >
        <Box className="action-buttons">
          <Button
            variant="primary"
            size="large"
            fullWidth
            onClick={handleNext}
            disabled={!isAnswerValid()}
            className="next-button"
          >
            {isLastQuestion ? 'Kết quả' : 'Tiếp theo'}
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default SurveyScreen;
