import React, { useState, useEffect } from 'react';
import { Box, Page, useNavigate, useSnackbar } from 'zmp-ui';
import { authorize, AppError, getPhoneNumber, getAccessToken, getUserInfo } from 'zmp-sdk';
import { DotLottieReact } from '@lottiefiles/dotlottie-react';
import GameHeader from './GameHeader';
import selectButtonImage from '@/static/select-button.png';
import surveyBgImage from '@/static/survey/bg-survey.png';
import numberBgImage from '@/static/survey/number-q-bg.png';
import questionBgImage from '@/static/survey/question-bg.png';
import flowerImage from '@/static/flower.png';
import noteBookImage from '@/static/survey/note-book.png';
import bgLoadingImage from '@/static/survey/bg-loading.png';
import img0 from '@/static/survey/0.png';
import img1 from '@/static/survey/1.png';
import img2 from '@/static/survey/2.png';
import img3 from '@/static/survey/3.png';
import img10 from '@/static/survey/10.png';
import img11 from '@/static/survey/11.png';
import img12 from '@/static/survey/12.png';
import { trackScreenView, trackButtonClick, trackSurveyResponse, trackSurveyComplete, trackError } from '@/utils/ga4-tracking';

interface Question {
  id: string;
  type: 'checkbox';
  question: string;
  options: string[];
  required: boolean;
  allowMultiple?: boolean;
}

interface SurveyResponse {
  questionId: string;
  answer: string | string[] | number;
}

interface ApiProcessResponse {
  status: 'success' | 'error';
  message: string;
  url?: string;
}

interface SurveyScreenProps {
  onSurveyComplete: (responses: SurveyResponse[], userData?: any) => void;
  uploadedImages: File[];
}

/**
 * Process game results by preparing form data and sending to the API endpoint
 * @param userName - The user's name (will default to 'Anonymous' if not provided)
 * @param uploadedImages - Array of uploaded image files (optional)
 * @param score - The score value (defaults to 1)
 * @param retryCount - Number of retry attempts (internal use)
 * @returns Promise<ApiProcessResponse> - The API response containing status, message, and optional url
 * @throws Error if the API call fails or returns invalid data
 */
export const processGameResults = async (
  userName: string | null | undefined,
  uploadedImages: File[],
  score: number = 1,
  retryCount: number = 0
): Promise<ApiProcessResponse> => {

  try {
    // Prepare API request with FormData
    const formData = new FormData();
    formData.append('user_name', userName || 'Anonymous');
    formData.append('score', score.toString());

    // Add image file if available
    if (uploadedImages.length > 0 && uploadedImages[0]) {
      formData.append('img', uploadedImages[0]);
    }
    console.log("FormData:", formData);

    // Make API call using standard fetch
    const apiUrl = 'https://ensure.lifesup.ai/api/process';
    const response = await fetch(apiUrl, {
      method: 'POST',
      body: formData,
    });
    console.log("API response:", response);

    // Handle response
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const result = await response.json() as ApiProcessResponse;

    // Validate response structure
    if (!result.status || !result.message) {
      throw new Error('Invalid API response structure: missing required fields');
    }

    // If status is success, ensure url field exists
    if (result.status === 'success' && !result.url) {
      throw new Error('API response missing url field for successful status');
    }

    return result;
  } catch (error) {
    // Log error for tracking
    const errorMessage = error instanceof Error ? error.message : String(error);
    const errorContext = {
      message: errorMessage,
      retryCount,
      hasImage: uploadedImages.length > 0,
      userName: userName || 'Anonymous',
    };

    await trackError('api_process_error', JSON.stringify(errorContext), 'processGameResults');

    // Re-throw error for calling component to handle
    throw error;
  }
};

// Custom Checkbox Component
interface CustomCheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ checked, onChange, label }) => {
  return (
    <Box
      className="custom-checkbox-container"
      onClick={() => onChange(!checked)}
    >
      <Box className="custom-checkbox">
        {checked && (
          <svg viewBox="0 0 25 15" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M6.93903 14.1353C11.2284 9.81297 13.1179 6.10297 24.0403 1.21877C24.246 1.12677 24.2863 0.846883 24.0961 0.726034C19.0149 -2.50197 9.54064 5.84049 6.07178 9.9298C6.03248 9.97613 5.96371 9.97451 5.92307 9.92935L1.98442 5.55308C1.69824 5.2351 1.23495 5.14736 0.852311 5.33867L0.790526 5.36957C0.604505 5.46258 0.463052 5.62567 0.397284 5.82298C0.146177 6.5763 0.251118 7.40292 0.682497 8.0696L4.41695 13.841C4.98423 14.7177 6.20348 14.8765 6.93903 14.1353Z" fill="#0C1BAC" />
          </svg>
        )}
      </Box>
      <span className="custom-checkbox-label">{label}</span>
    </Box>
  );
};

const SurveyScreen: React.FC<SurveyScreenProps> = ({ onSurveyComplete, uploadedImages }) => {
  const navigate = useNavigate();
  const [responses, setResponses] = useState<SurveyResponse[]>([]);
  const [userInfo, setUserInfoState] = useState({
    name: "",
    avatar: "",
    idByOA: null as string | null,
    mobile: null as string | null
  });
  const { openSnackbar } = useSnackbar();

  const [showLoadingDialog, setShowLoadingDialog] = useState(false);
  const [surveyStartTime] = useState(Date.now());
  const [visibleImages, setVisibleImages] = useState<number[]>([]);
  const [typedTitle, setTypedTitle] = useState('');
  const [typedSubtitle, setTypedSubtitle] = useState('');

  useEffect(() => {
    // Track screen view when component mounts
    trackScreenView('SurveyScreen', 'Game');
  }, []);

  // Handle sequential image animation
  useEffect(() => {
    if (showLoadingDialog) {
      // Reset visible images when dialog opens
      setVisibleImages([]);

      // Image sequence: 10, 11, 12, 0, 1, 2, 3
      const imageSequence = [10, 11, 12, 0, 1, 2, 3];
      const timers: number[] = [];

      imageSequence.forEach((imageNum, index) => {
        const timer = setTimeout(() => {
          setVisibleImages(prev => [...prev, imageNum]);
        }, index * 300); // 0.3 second delay between each image
        timers.push(timer);
      });

      // Cleanup timers on unmount or when dialog closes
      return () => {
        timers.forEach(timer => clearTimeout(timer));
      };
    }
  }, [showLoadingDialog]);

  // Typing animation for loading dialog
  useEffect(() => {
    if (showLoadingDialog) {
      // Reset typed text
      setTypedTitle('');
      setTypedSubtitle('');

      const titleText = 'Phiếu bé ngoan \nđang được chuẩn bị';
      const subtitleText = 'Vui lòng chờ trong 1 phút nhé';
      let titleIndex = 0;
      let subtitleIndex = 0;
      let subtitleInterval: number;

      // Type title
      const titleInterval = setInterval(() => {
        if (titleIndex < titleText.length) {
          // Get character before incrementing index
          const char = titleText[titleIndex];
          setTypedTitle(prev => prev + char);
          titleIndex++;
        } else {
          clearInterval(titleInterval);

          // Type subtitle after title completes
          subtitleInterval = setInterval(() => {
            if (subtitleIndex < subtitleText.length) {
              // Get character before incrementing index
              const char = subtitleText[subtitleIndex];
              setTypedSubtitle(prev => prev + char);
              subtitleIndex++;
            } else {
              clearInterval(subtitleInterval);
            }
          }, 50);
        }
      }, 50);

      return () => {
        clearInterval(titleInterval);
        if (subtitleInterval) {
          clearInterval(subtitleInterval);
        }
      };
    } else {
      // Reset when dialog closes
      setTypedTitle('');
      setTypedSubtitle('');
    }
  }, [showLoadingDialog]);


  // Survey questions về chủ đề gia đình và mùa Vu Lan
  const questions: Question[] = [
    {
      id: 'q1',
      type: 'checkbox',
      question: 'Thời gian qua, bạn đã "vượt" chính mình như thế nào?',
      options: [
        'Biến "chưa biết" thành một chứng chỉ/bằng cấp đàng hoàng nè',
        'Dọn đến nơi ở mới – hoặc biến nhà thành tổ ấm thật sự',
        'Hoàn thành công việc/dự án mà bạn từng nghĩ "chắc mình sẽ không làm được đâu"',
        'Học cách "lắng nghe cơ thể mình" và chăm sóc đúng cách',
        'Vừa chăm được mình, vừa lo chu toàn cho sức khỏe ba mẹ'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q2',
      type: 'checkbox',
      question: 'Trước những bất định trong cuộc sống, đâu là động lực khiến bạn tiếp tục cố gắng?',
      options: [
        'Nhớ lại lý do mình bắt đầu',
        'Tự nhủ: "Rồi sẽ ổn thôi mà"',
        'Nghỉ ngơi để tiếp thêm năng lượng',
        'Nghĩ đến gia đình, Cha Mẹ – điểm tựa vững chắc cho tôi',
        'Tìm kiếm sự sẻ chia từ bạn bè và những người xung quanh'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q3',
      type: 'checkbox',
      question: 'Trên hành trình nỗ lực và cố gắng ấy, gia đình với bạn là…?',
      options: [
        'Điểm tựa vững vàng tiếp thêm sức mạnh cho tôi',
        'Cái ô che chở tôi giữa những mưa cuộc đời',
        'Vòng tay rộng mở đón tôi trở về trong mọi hoàn cảnh',
        'Lời động viên truyền động lực',
        'Tấm gương sáng truyền cảm hứng để cho tôi thấy thế nào là sống tử tế và kiên cường'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q4',
      type: 'checkbox',
      question: 'Thay vì nói "con thương ba mẹ", bạn thường thể hiện sự quan tâm đến Cha Mẹ bằng cách nào?',
      options: [
        'Hỏi thăm sức khỏe thường xuyên, không chỉ "lúc rảnh"',
        'Dẫn ba mẹ đi khám định kỳ, không đợi khi ốm mới đi',
        'Vào bếp tự tay nấu bữa cơm gia đình cho ba mẹ',
        'Lắng nghe và dành thời gian trò chuyện với Cha Mẹ',
        'Nhắc ba mẹ uống sữa mỗi ngày, như cách họ từng chăm tôi khi bé'
      ],
      required: true,
      allowMultiple: true
    },
    {
      id: 'q5',
      type: 'checkbox',
      question: 'Từ mùa Vu Lan này, bạn muốn cùng ba mẹ tạo nên những khoảnh khắc nào?',
      options: [
        'Cùng đi xem bộ phim ba mẹ yêu thích',
        'Cùng dự một đêm nhạc gợi nhớ thanh xuân',
        'Cùng kể chuyện cũ, nghe ba mẹ nói "ngày xưa…"',
        'Cùng nấu một bữa cơm đàng vị nhà',
        'Cùng hát lại bản karaoke "hồi sáu tồng mà vui"',
        'Cùng tập thể dục – mỗi người một động tác, nhưng cùng nhịp',
        'Cùng đi du lịch – có thể gần thôi, nhưng là cùng nhau',
        'Cùng ngồi quán café, nhìn phố phường trôi chầm chậm'
      ],
      required: true,
      allowMultiple: true
    }
  ];

  const handleAnswerChange = (questionId: string, answer: string | string[] | number) => {
    const newResponse: SurveyResponse = {
      questionId,
      answer
    };

    const updatedResponses = responses.filter(r => r.questionId !== questionId);
    updatedResponses.push(newResponse);
    setResponses(updatedResponses);
  };

  const handleCheckboxChange = async (questionId: string, option: string, checked: boolean) => {
    const currentAnswer = getAnswerForQuestion(questionId);
    let newAnswer: string[] = [];

    if (Array.isArray(currentAnswer)) {
      newAnswer = [...currentAnswer];
    }

    if (checked) {
      if (!newAnswer.includes(option)) {
        newAnswer.push(option);
      }
    } else {
      newAnswer = newAnswer.filter(item => item !== option);
    }

    handleAnswerChange(questionId, newAnswer);

    // Track survey response
    const question = questions.find(q => q.id === questionId);
    if (question) {
      const sessionId = localStorage.getItem('gameState') ?
        JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
        'unknown';
      await trackSurveyResponse(sessionId, questionId, question.question, 'checkbox');
    }
  };


  const handleGetUserInfo = () => {
    return new Promise<{
      name: string;
      avatar: string;
      idByOA: string | null;
      mobile: string | null;
    }>((resolve, reject) => {
      getUserInfo({
        autoRequestPermission: false,
        success: (res) => {
          console.log("getUserInfo success:", res);

          const userData = {
            name: res.userInfo.name || "",
            avatar: res.userInfo.avatar || "",
            idByOA: res.userInfo.idByOA || null,
            mobile: null, // Giữ nguyên số điện thoại hiện tại
          };
          console.log("get user info :", userData);


          // Lưu trực tiếp vào context với userData mới
          const contextData = {
            name: userData.name || undefined,
            avatar: userData.avatar || undefined,
            idByOA: userData.idByOA,
            mobile: userData.mobile || undefined,
          };
          console.log("User info saved to context:", contextData);
          resolve(userData);
        },
        fail: (error) => {
          console.error("Failed to get user info:", error);

          if (error && (error as AppError).code === -1401) {
            console.log("Người dùng từ chối cung cấp tên và ảnh đại diện");
          }

          reject(error);
        }
      });
    });
  };

  const handleGetPhoneNumber = () => {

    return new Promise((resolve, reject) => {
      getPhoneNumber({
        success: async (data) => {
          const { token } = data;
          console.log("Phone token:", data);

          try {
            const accessToken = await getAccessToken();

            const response = await fetch('https://automation.lifesup.ai/webhook/c18e43a8-158e-47ce-97a0-15a75b2f0b63', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({
                code: token,
                access_token: accessToken,
                secret_key: 'TXWCPFL3hBFlt6uC82N7'
              }),
            });

            const result = await response.json();
            console.log("API result:", result);

            if (result.error === 0) {
              const phoneNum = result.data.number;

              setUserInfoState(prev => {
                const updatedUserInfo = {
                  ...prev,
                  mobile: phoneNum
                };

                console.log("Updated user info:", updatedUserInfo);
                resolve(phoneNum);
                return updatedUserInfo;
              });
            } else {
              console.error("Lỗi khi lấy số điện thoại:", result.message);
              reject(new Error(result.message));
            }
          } catch (error) {
            console.error("Lỗi khi gọi API server:", error);
            reject(error);
          }
        },
        fail: (error) => {
          console.error("Lỗi khi lấy số điện thoại từ Zalo:", error);
          if (error.code === -1402) {
            console.log("Người dùng từ chối cung cấp số điện thoại");
          }
          reject(error);
        }
      });
    });
  };


  const handleStartPhone = async () => {
    try {
      const data = await authorize({
        scopes: ["scope.userInfo", "scope.userPhonenumber"],
      });
      console.log("user info", data["scope.userInfo"]);
      console.log("user phone", data["scope.userPhonenumber"]);

      // Sau khi cấp quyền, kiểm tra lại settings
      if (data["scope.userInfo"] && data["scope.userPhonenumber"]) {
        // Thực hiện cả 2 hàm song song
        const [userInfoResult] = await Promise.all([
          handleGetUserInfo(),
          handleGetPhoneNumber()
        ]);

        // Sau khi cả 2 hàm hoàn thành, hiển thị loading dialog
        setShowLoadingDialog(true);

        try {
          // Call the reusable API function with parameters
          const apiResult = await processGameResults("1", uploadedImages, 1);
          console.log("API result:", apiResult);

          if (apiResult.status === 'success' && apiResult.url) {
            // Close loading dialog
            setShowLoadingDialog(false);

            // Track survey completion
            const sessionId = localStorage.getItem('gameState') ?
              JSON.parse(localStorage.getItem('gameState') || '{}').sessionId :
              'unknown';
            const completionTime = Math.floor((Date.now() - surveyStartTime) / 1000);
            await trackSurveyComplete(sessionId, questions.length, completionTime);

            // Navigate to results with API response data including video URL
            onSurveyComplete(responses, {
              ...userInfoResult,
              videoUrl: apiResult.url,
              apiMessage: apiResult.message
            });
            navigate('/results');
          } else {
            throw new Error(apiResult.message || 'API processing failed');
          }

        } catch (apiError) {
          console.error('API call failed:', apiError);
          await trackError('api_process_error', String(apiError), 'SurveyScreen');

          openSnackbar({
            text: "Không thể xử lý kết quả. Vui lòng thử lại sau.",
            type: "error"
          });
        }
      }
    } catch (error) {
      const code = (error as AppError).code;
      if (code === -201) {
        console.log("Người dùng đã từ chối cấp quyền");
        openSnackbar({
          text: "Vui lòng cấp quyền để tiếp tục xem kết quả",
          type: "error"
        });
      } else {
        console.log("Lỗi khác", error);
        openSnackbar({
          text: "Đã có lỗi xảy ra. Vui lòng thử lại",
          type: "error"
        });
      }
    }
  };

  const handleSubmit = async () => {
    // Track submit button click
    await trackButtonClick('submit_survey', 'SurveyScreen', {
      button_location: 'footer',
      questions_answered: responses.length,
      button_text: 'XEM KẾT QUẢ'
    });
    handleStartPhone();
  };

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (showLoadingDialog) {
        // setShowLoadingDialog(false);
      }
    };
  }, [showLoadingDialog]);

  const getAnswerForQuestion = (questionId: string) => {
    const response = responses.find(r => r.questionId === questionId);
    return response?.answer || '';
  };

  const isQuestionAnswered = (question: Question) => {
    const answer = getAnswerForQuestion(question.id);

    if (question.type === 'checkbox') {
      return Array.isArray(answer) && answer.length > 0;
    }

    return answer !== '';
  };

  const areAllRequiredQuestionsAnswered = () => {
    return questions.every(question => isQuestionAnswered(question));
  };

  const testDialog = async () => {
    await processGameResults("1", uploadedImages, 1);
  }


  return (
    <Page className="survey-screen">
      {/* Loading Dialog */}
      {showLoadingDialog && (
        <Box className="loading-dialog-overlay">
          <Box className="loading-dialog" style={{ backgroundImage: `url(${bgLoadingImage})`, backgroundSize: 'cover', backgroundPosition: 'center' }}>
            <Box className="loading-content" style={{ display: 'flex', flexDirection: 'column', alignItems: 'center', padding: '20px' }}>
              <h3 className="loading-title gradient-text">
                {typedTitle.split('\n').map((line, index, array) => (
                  <React.Fragment key={index}>
                    {line}
                    {index < array.length - 1 && <br />}
                  </React.Fragment>
                ))}
              </h3>
              <p className="loading-subtitle gradient-text">{typedSubtitle}</p>

              {/* Notebook image element */}
              <Box style={{ position: 'relative', marginTop: '20px' }}>
                <img src={noteBookImage} alt="Notebook" style={{ width: '400px', height: 'auto' }} />

                {/* Top row images - positioned relative to notebook */}
                <Box style={{ position: 'absolute', top: '64px', left: '45%', transform: 'translateX(-50%)', display: 'flex', gap: '1px' }}>
                  <img
                    src={img10}
                    alt="10"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '8px',
                      opacity: visibleImages.includes(10) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                  <img
                    src={img11}
                    alt="11"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '4px',
                      opacity: visibleImages.includes(11) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                  <img
                    src={img12}
                    alt="12"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '2px',
                      opacity: visibleImages.includes(12) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                </Box>

                {/* Middle row images - positioned relative to notebook */}
                <Box style={{ position: 'absolute', top: '80px', left: '45%', transform: 'translateX(-50%)', display: 'flex', gap: '1px' }}>
                  <img
                    src={img0}
                    alt="0"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '8px',
                      opacity: visibleImages.includes(0) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                  <img
                    src={img1}
                    alt="1"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '6px',
                      opacity: visibleImages.includes(1) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                  <img
                    src={img2}
                    alt="2"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '4px',
                      opacity: visibleImages.includes(2) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                  <img
                    src={img3}
                    alt="3"
                    style={{
                      height: '12px',
                      width: 'auto',
                      marginTop: '2px',
                      opacity: visibleImages.includes(3) ? 1 : 0,
                      transition: 'opacity 0.5s ease-in-out'
                    }}
                  />
                </Box>

                {/* Lottie animation */}
                <Box style={{ position: 'absolute', bottom: '72px', left: '57%', transform: 'translateX(-50%) rotate(-16deg)', display: 'flex', gap: '2px', width: "80px" }}>
                  <DotLottieReact
                    src="https://lottie.host/ade1cf84-e344-40c3-988c-669fec36a046/m7gtLYuXZ8.lottie"
                    loop
                    autoplay
                  />
                </Box>
              </Box>
            </Box>
          </Box>
        </Box>
      )}
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box className="main-content" style={{ position: 'relative' }}>
        {/* Hero Section */}
        <Box className="hero-section">
          {/* Header Section */}
          <Box className="survey-header-section">
            <h3 className="gradient-text survey-line1">Bé ngoan sẽ được</h3>
            <h2 className="gradient-text survey-line2">bao nhiêu hoa hồng</h2>
            <p className="gradient-text survey-line3">
              Cùng trả lời <span className="number-highlight">5</span> câu hỏi sau đây
            </p>
          </Box>

          <img src={flowerImage} alt="Flower" className="flower-image" />


          {/* Survey Body Section */}
          <Box
            className="survey-body-section"
            style={{ backgroundImage: `url(${surveyBgImage})` }}
          >
            {questions.map((question, index) => (
              <Box key={question.id} className="survey-question-item">
                {/* Question Number */}
                <Box
                  className="question-number-box"
                  style={{ backgroundImage: `url(${numberBgImage})` }}
                >
                  <span className="question-number-text">Câu {index + 1}</span>
                </Box>

                {/* Question Text */}
                <Box className="question-text-box">
                  <img
                    src={questionBgImage}
                    alt="Question background"
                    className="question-bg-image"
                  />
                  <p className="question-main-text">{question.question}</p>
                </Box>

                {/* Note Text */}
                <p className="question-note-text">* Lưu ý: Bạn được chọn nhiều đáp án</p>

                {/* Answer Options */}
                <Box className="answer-options-container">
                  {question.options.map((option, optIndex) => {
                    const currentAnswer = getAnswerForQuestion(question.id);
                    const isChecked = Array.isArray(currentAnswer) && currentAnswer.includes(option);

                    return (
                      <Box key={optIndex} className="option-item">
                        <CustomCheckbox
                          checked={isChecked}
                          onChange={(checked) => handleCheckboxChange(question.id, option, checked)}
                          label={option}
                        />
                      </Box>
                    );

                  })}
                </Box>
              </Box>
            ))}
          </Box>
        </Box>
      </Box>

      {/* Selected Zone (Footer) - Only Action Buttons */}
      <Box className="selected-zone">
        <Box className="action-buttons">
          <button
            className="image-button"
            onClick={handleSubmit}
            disabled={!areAllRequiredQuestionsAnswered()}
          >
            <img src={selectButtonImage} alt="Submit Survey" />
            <span className="button-text gradient-text">XEM KẾT QUẢ</span>
          </button>
        </Box>
      </Box>
    </Page>
  );
};

export default SurveyScreen;
