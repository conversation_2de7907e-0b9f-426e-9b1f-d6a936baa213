import React from 'react';
import { <PERSON><PERSON>, <PERSON>, Text, Page } from 'zmp-ui';
import { useNavigate } from 'zmp-ui';
import bgImage from '@/static/bg.png';
import selectZoneImage from '@/static/select-zone.png';
import GameHeader from './GameHeader';

interface WelcomeScreenProps {
  onStart: () => void;
}

const WelcomeScreen: React.FC<WelcomeScreenProps> = ({ onStart }) => {
  const navigate = useNavigate();

  const handleStartGame = () => {
    onStart();
    navigate('/upload');
  };

  return (
    <Page
      className="welcome-screen"
      style={{
        backgroundImage: `url(${bgImage})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
        backgroundAttachment: 'fixed',
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        paddingTop: '48px' // Account for fixed header
      }}
    >
      {/* Header */}
      <GameHeader />

      {/* Main Content Area */}
      <Box
        className="main-content"
        style={{
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center',
          padding: '20px',
          maxWidth: '600px',
          margin: '0 auto',
          width: '100%'
        }}
      >
        {/* Progress Indicator */}
        <Box className="progress-indicator">
          <div className="progress-step active">1</div>
          <div className="progress-step">2</div>
          <div className="progress-step">3</div>
          <div className="progress-step">4</div>
        </Box>

        {/* Hero Section */}

      </Box>

      {/* Selected Zone (Footer) */}
      <Box
        className="selected-zone"
        style={{
          backgroundImage: `url(${selectZoneImage})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat',
          width: '100%',
          position: 'relative',
          marginTop: 'auto', // Push to bottom
          padding: '20px',
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          minHeight: '120px' // Ensure adequate footer height
        }}
      >
        {/* Action Buttons */}
        <Box className="action-buttons" style={{ maxWidth: '600px', width: '100%' }}>
          <Button
            variant="primary"
            size="large"
            fullWidth
            onClick={handleStartGame}
            className="start-button"
          >
            Chơi
          </Button>
        </Box>
      </Box>
    </Page>
  );
};

export default WelcomeScreen;
