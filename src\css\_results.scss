// Results Screen Styles
.results-screen {
  &.loading {
    .selected-zone {
      &.loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        text-align: center;
        color: var(--white);
      }
    }
  }
  
  .selected-zone {
    max-width: 800px;
  }

  // Main content specific to results screen
  .main-content.results-main-content {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    min-height: 100%;
  }

  // Hero section for results
  .hero-section {
    display: flex;
    flex-direction: column;
    flex: 1;
    justify-content: space-between;
  }

  // Video section wrapper
  .video-section.video-wrapper {
    width: 100%;
    padding-top: 20px;
    display: flex;
    justify-content: center;
  }

  // Video placeholder container
  .video-placeholder-container {
    width: 90%;
    max-width: 400px;
    aspect-ratio: 9/16;
    background-color: #f0f0f0;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px dashed #ccc;
  }

  // Video placeholder text
  .video-placeholder-text {
    color: #999;
  }

  // Text content section
  .text-content {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  // Share heading styles
  .share-heading {
    font-size: 20px;
    font-style: italic;
    font-weight: 700;
    line-height: 16px;
    text-align: center;
  }

  // Share subheading styles
  .share-subheading {
    font-size: 14px;
    font-style: italic;
    font-weight: 700;
    line-height: 16px;
    text-align: center;
  }

  // Prizes section
  .prizes-section {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  // Prizes image
  .prizes-image {
    width: 100%;
    max-width: 400px;
    height: auto;
  }



  .action-buttons {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);

    button {
      flex: 1;
      min-height: 48px;
    }
  }

}

// Responsive Design
@media (max-width: 768px) {
  .results-screen {
    .action-buttons {
      flex-direction: column;
    }


    // Video placeholder on tablets
    .video-placeholder-container {
      width: 85%;
      max-width: 350px;
    }
  }
}

@media (max-width: 480px) {
  .results-screen {

    // Adjust text sizes on mobile
    .share-heading {
      font-size: 18px;
      line-height: 20px;
    }

    .share-subheading {
      font-size: 13px;
      line-height: 18px;
    }

    // Video placeholder on mobile
    .video-placeholder-container {
      width: 80%;
      max-width: 300px;
    }

    // Prizes image on mobile
    .prizes-image {
      max-width: 90%;
    }
  }
}