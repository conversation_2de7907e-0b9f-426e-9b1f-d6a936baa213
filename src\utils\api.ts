// Mock API Functions for Game (No Backend Required)

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GameSubmissionData {
  sessionId: string;
  images: File[];
  surveyResponses: any[];
  startTime: string;
  completionTime: string;
  userAgent?: string;
  screenResolution?: string;
}

export interface GameAnalytics {
  sessionId: string;
  event: string;
  data: any;
  timestamp: string;
}

// Mock delay for realistic API simulation
const mockDelay = (ms: number = 1000) => new Promise(resolve => setTimeout(resolve, ms));

// Mock API functions that simulate backend responses
const mockApiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {},
  mockData?: T
): Promise<ApiResponse<T>> => {
  await mockDelay(500 + Math.random() * 1000); // Random delay 500-1500ms

  // Simulate occasional failures (5% chance)
  if (Math.random() < 0.05) {
    return {
      success: false,
      error: 'Simulated network error'
    };
  }

  return {
    success: true,
    data: mockData,
    message: 'Success'
  };
};

// Mock Game Session APIs
export const createGameSession = async (): Promise<ApiResponse<{ sessionId: string }>> => {
  const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  return mockApiRequest('/game/session', { method: 'POST' }, { sessionId });
};

export const updateGameSession = async (
  sessionId: string,
  data: Partial<GameSubmissionData>
): Promise<ApiResponse> => {
  console.log('Mock: Updating session', sessionId, data);
  return mockApiRequest(`/game/session/${sessionId}`, { method: 'PUT' });
};

export const getGameSession = async (sessionId: string): Promise<ApiResponse<GameSubmissionData>> => {
  console.log('Mock: Getting session', sessionId);
  return mockApiRequest(`/game/session/${sessionId}`, { method: 'GET' });
};

// Image Upload APIs
export const uploadImages = async (
  sessionId: string,
  images: File[]
): Promise<ApiResponse<{ uploadedFiles: string[] }>> => {
  try {
    const formData = new FormData();
    formData.append('sessionId', sessionId);

    images.forEach((image, index) => {
      formData.append(`image_${index}`, image);
    });

    const response = await fetch(`${API_BASE_URL}/game/upload`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Image upload failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Upload failed'
    };
  }
};

export const deleteImage = async (
  sessionId: string,
  imageId: string
): Promise<ApiResponse> => {
  return apiRequest(`/game/upload/${sessionId}/${imageId}`, {
    method: 'DELETE'
  });
};

// Survey APIs
export const getQuestions = async (): Promise<ApiResponse<any[]>> => {
  return apiRequest('/game/questions', {
    method: 'GET'
  });
};

export const submitSurveyResponse = async (
  sessionId: string,
  questionId: string,
  answer: string | string[] | number
): Promise<ApiResponse> => {
  return apiRequest('/game/survey', {
    method: 'POST',
    body: JSON.stringify({
      sessionId,
      questionId,
      answer,
      timestamp: new Date().toISOString()
    })
  });
};

export const submitAllSurveyResponses = async (
  sessionId: string,
  responses: any[]
): Promise<ApiResponse> => {
  return apiRequest('/game/survey/batch', {
    method: 'POST',
    body: JSON.stringify({
      sessionId,
      responses: responses.map(r => ({
        ...r,
        timestamp: new Date().toISOString()
      }))
    })
  });
};

// Game Completion APIs
export const submitGameCompletion = async (
  data: GameSubmissionData
): Promise<ApiResponse<{ results: any }>> => {
  try {
    const formData = new FormData();

    // Add text data
    formData.append('sessionId', data.sessionId);
    formData.append('surveyResponses', JSON.stringify(data.surveyResponses));
    formData.append('startTime', data.startTime);
    formData.append('completionTime', data.completionTime);

    if (data.userAgent) {
      formData.append('userAgent', data.userAgent);
    }

    if (data.screenResolution) {
      formData.append('screenResolution', data.screenResolution);
    }

    // Add images
    data.images.forEach((image, index) => {
      formData.append(`image_${index}`, image);
    });

    const response = await fetch(`${API_BASE_URL}/game/complete`, {
      method: 'POST',
      body: formData
    });

    if (!response.ok) {
      throw new Error(`Submission failed: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Game submission failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Submission failed'
    };
  }
};

export const getGameResults = async (sessionId: string): Promise<ApiResponse<any>> => {
  return apiRequest(`/game/results/${sessionId}`, {
    method: 'GET'
  });
};

// Analytics APIs
export const trackEvent = async (analytics: GameAnalytics): Promise<ApiResponse> => {
  return apiRequest('/game/analytics', {
    method: 'POST',
    body: JSON.stringify(analytics)
  });
};

export const trackScreenView = async (
  sessionId: string,
  screenName: string,
  timeSpent?: number
): Promise<ApiResponse> => {
  return trackEvent({
    sessionId,
    event: 'screen_view',
    data: { screenName, timeSpent },
    timestamp: new Date().toISOString()
  });
};

export const trackUserAction = async (
  sessionId: string,
  action: string,
  data?: any
): Promise<ApiResponse> => {
  return trackEvent({
    sessionId,
    event: 'user_action',
    data: { action, ...data },
    timestamp: new Date().toISOString()
  });
};

// Utility Functions
export const checkApiHealth = async (): Promise<boolean> => {
  try {
    const response = await apiRequest('/health');
    return response.success;
  } catch {
    return false;
  }
};

export const getApiStatus = async (): Promise<ApiResponse<{
  status: string;
  version: string;
  uptime: number;
}>> => {
  return apiRequest('/status');
};

// Error Handling
export class ApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message);
    this.name = 'ApiError';
  }
}

// Retry mechanism for failed requests
export const retryApiRequest = async <T>(
  requestFn: () => Promise<ApiResponse<T>>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<ApiResponse<T>> => {
  let lastError: any;

  for (let i = 0; i < maxRetries; i++) {
    try {
      const result = await requestFn();
      if (result.success) {
        return result;
      }
      lastError = result.error;
    } catch (error) {
      lastError = error;
    }

    if (i < maxRetries - 1) {
      await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, i)));
    }
  }

  return {
    success: false,
    error: `Request failed after ${maxRetries} attempts: ${lastError}`
  };
};

// Offline support
export const isOnline = (): boolean => {
  return navigator.onLine;
};

export const queueOfflineRequest = (request: any): void => {
  const queue = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
  queue.push({
    ...request,
    timestamp: new Date().toISOString()
  });
  localStorage.setItem('offlineQueue', JSON.stringify(queue));
};

export const processOfflineQueue = async (): Promise<void> => {
  if (!isOnline()) return;

  const queue = JSON.parse(localStorage.getItem('offlineQueue') || '[]');
  if (queue.length === 0) return;

  console.log(`Processing ${queue.length} offline requests...`);

  const processed: any[] = [];

  for (const request of queue) {
    try {
      // Process each queued request
      await apiRequest(request.endpoint, request.options);
      processed.push(request);
    } catch (error) {
      console.error('Failed to process offline request:', error);
    }
  }

  // Remove processed requests from queue
  const remaining = queue.filter((req: any) => !processed.includes(req));
  localStorage.setItem('offlineQueue', JSON.stringify(remaining));

  console.log(`Processed ${processed.length} offline requests`);
};

// Initialize offline queue processing
if (typeof window !== 'undefined') {
  window.addEventListener('online', processOfflineQueue);
}
