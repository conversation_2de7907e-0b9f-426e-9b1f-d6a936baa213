// Google Analytics 4 Tracking for Zalo Mini Program
// CORS-compliant implementation with multiple fallback options

const MEASUREMENT_ID = "G-RQTF39YPM0";
const API_SECRET = "11862380831";
const GA4_ENDPOINT = "https://www.google-analytics.com/mp/collect";

// Configuration for different tracking methods
const TRACKING_CONFIG = {
  // Option 1: Your backend proxy endpoint (recommended)
  proxyEndpoint: null, // Disabled - no backend server available
  
  // Option 2: Use Google Analytics gtag.js (primary method for frontend-only)
  useGtag: true,
  
  // Option 3: Batch events and send via beacon API
  useBatchMode: false, // Disabled due to CORS restrictions
  batchInterval: 5000, // 5 seconds
  
  // Option 4: Use Zalo Mini App analytics (if available)
  useZaloAnalytics: true,
  
  // Environment detection
  isZaloMiniProgram: typeof window !== 'undefined' && window.zalo !== undefined,
  isDevelopment: window.location.hostname === 'localhost'
};

// Event queue for batch processing
let eventQueue: any[] = [];
let batchTimer: NodeJS.Timeout | null = null;

// Generate a client ID for the user (persisted in localStorage)
const getClientId = (): string => {
  try {
    let clientId = localStorage.getItem('ga4_client_id');
    if (!clientId) {
      clientId = `${Date.now()}.${Math.random().toString(36).substring(2, 15)}`;
      localStorage.setItem('ga4_client_id', clientId);
    }
    return clientId;
  } catch (e) {
    // Fallback for environments where localStorage might not work
    return `${Date.now()}.${Math.random().toString(36).substring(2, 15)}`;
  }
};

// Generate a session ID (refreshed every 30 minutes)
const getSessionId = (): string => {
  try {
    const sessionKey = 'ga4_session_id';
    const sessionExpiry = 'ga4_session_expiry';
    
    const now = Date.now();
    const expiry = localStorage.getItem(sessionExpiry);
    
    if (!expiry || now > parseInt(expiry)) {
      const newSessionId = Math.random().toString(36).substring(2, 15);
      localStorage.setItem(sessionKey, newSessionId);
      localStorage.setItem(sessionExpiry, (now + 30 * 60 * 1000).toString()); // 30 minutes
      return newSessionId;
    }
    
    return localStorage.getItem(sessionKey) || '';
  } catch (e) {
    return Math.random().toString(36).substring(2, 15);
  }
};

// GA4 Event Interface
export interface GA4Event {
  name: string;
  params?: Record<string, any>;
}

// Initialize gtag if needed (for Option 2)
const initializeGtag = () => {
  if (typeof window !== 'undefined' && !window.gtag) {
    // Add gtag script
    const script = document.createElement('script');
    script.async = true;
    script.src = `https://www.googletagmanager.com/gtag/js?id=${MEASUREMENT_ID}`;
    document.head.appendChild(script);
    
    // Initialize gtag
    window.dataLayer = window.dataLayer || [];
    window.gtag = function() {
      window.dataLayer.push(arguments);
    };
    window.gtag('js', new Date());
    window.gtag('config', MEASUREMENT_ID, {
      send_page_view: false, // We'll send page views manually
    });
  }
};

// Option 1: Send via backend proxy (recommended)
const sendViaProxy = async (payload: any): Promise<boolean> => {
  try {
    const response = await fetch(TRACKING_CONFIG.proxyEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        measurementId: MEASUREMENT_ID,
        apiSecret: API_SECRET,
        payload
      })
    });
    
    return response.ok;
  } catch (error) {
    console.warn('Proxy tracking failed:', error);
    return false;
  }
};

// Option 2: Send via gtag.js
const sendViaGtag = (event: GA4Event): boolean => {
  try {
    if (window.gtag) {
      window.gtag('event', event.name, event.params);
      return true;
    }
    return false;
  } catch (error) {
    console.warn('Gtag tracking failed:', error);
    return false;
  }
};

// Option 3: Send via Beacon API (no CORS)
const sendViaBeacon = (payload: any): boolean => {
  try {
    if (navigator.sendBeacon) {
      const url = `${GA4_ENDPOINT}?measurement_id=${MEASUREMENT_ID}&api_secret=${API_SECRET}`;
      const blob = new Blob([JSON.stringify(payload)], { type: 'application/json' });
      return navigator.sendBeacon(url, blob);
    }
    return false;
  } catch (error) {
    console.warn('Beacon tracking failed:', error);
    return false;
  }
};

// Batch processor for events
const processBatchEvents = async () => {
  if (eventQueue.length === 0) return;
  
  const events = [...eventQueue];
  eventQueue = [];
  
  const payload = {
    client_id: getClientId(),
    events: events.map(event => ({
      name: event.name,
      params: {
        session_id: getSessionId(),
        engagement_time_msec: 100,
        ...event.params
      }
    }))
  };
  
  // Try sending via proxy first
  let sent = await sendViaProxy(payload);
  
  // Fallback to beacon API
  if (!sent) {
    sent = sendViaBeacon(payload);
  }
  
  if (!sent) {
    console.warn('Failed to send batch events');
  }
};

// Send event via Zalo Analytics (if available)
const sendViaZaloAnalytics = (event: GA4Event): boolean => {
  try {
    // Check if Zalo SDK analytics is available
    if (window.zalo && window.zalo.analytics) {
      window.zalo.analytics.trackEvent(event.name, event.params);
      return true;
    }
    // Alternative: Use Zalo's general tracking if available
    if (window.zalo && window.zalo.trackEvent) {
      window.zalo.trackEvent(event.name, event.params);
      return true;
    }
    return false;
  } catch (error) {
    console.warn('Zalo analytics failed:', error);
    return false;
  }
};

// Main event sending function with fallback strategies
export const sendGA4Event = async (event: GA4Event): Promise<void> => {
  try {
    const clientId = getClientId();
    const sessionId = getSessionId();
    
    // Prepare enhanced event params
    const enhancedParams = {
      session_id: sessionId,
      engagement_time_msec: 100,
      platform: TRACKING_CONFIG.isZaloMiniProgram ? 'zalo_mini_app' : 'web',
      ...event.params
    };
    
    let sent = false;
    
    // For Zalo Mini Program environment
    if (TRACKING_CONFIG.isZaloMiniProgram) {
      // 1. Try Zalo's native analytics first
      if (TRACKING_CONFIG.useZaloAnalytics) {
        sent = sendViaZaloAnalytics({
          name: event.name,
          params: enhancedParams
        });
      }
      
      // 2. Try gtag.js as fallback (may work in Zalo webview)
      if (!sent && TRACKING_CONFIG.useGtag) {
        initializeGtag(); // Ensure gtag is initialized
        sent = sendViaGtag({
          name: event.name,
          params: enhancedParams
        });
      }
    } else {
      // For regular web environment
      
      // 1. Try proxy endpoint if configured
      if (TRACKING_CONFIG.proxyEndpoint) {
        const payload = {
          client_id: clientId,
          events: [{
            name: event.name,
            params: enhancedParams
          }]
        };
        sent = await sendViaProxy(payload);
      }
      
      // 2. Try gtag.js (primary method for web)
      if (!sent && TRACKING_CONFIG.useGtag) {
        initializeGtag(); // Ensure gtag is initialized
        sent = sendViaGtag({
          name: event.name,
          params: enhancedParams
        });
      }
    }
    
    // 3. In development, always log the event for debugging
    if (TRACKING_CONFIG.isDevelopment) {
      console.log('GA4 Event:', {
        name: event.name,
        params: enhancedParams,
        sent: sent,
        environment: TRACKING_CONFIG.isZaloMiniProgram ? 'Zalo Mini App' : 'Web'
      });
    }
    
    // 4. If nothing worked, store event for later retry (optional)
    if (!sent && !TRACKING_CONFIG.isDevelopment) {
      console.warn('GA4 tracking failed for event:', event.name);
      // You could implement local storage queue here for retry logic
    }
    
  } catch (error) {
    console.error('GA4 tracking error:', error);
    // Don't throw - tracking failures shouldn't break the app
  }
};

// Predefined event tracking functions
export const trackScreenView = async (screenName: string, screenClass?: string): Promise<void> => {
  await sendGA4Event({
    name: 'page_view',
    params: {
      page_title: screenName,
      page_location: window.location.href,
      page_path: window.location.pathname,
      screen_name: screenName,
      screen_class: screenClass || screenName
    }
  });
};

export const trackButtonClick = async (
  buttonName: string,
  screenName: string,
  additionalParams?: Record<string, any>
): Promise<void> => {
  await sendGA4Event({
    name: 'button_click',
    params: {
      button_name: buttonName,
      screen_name: screenName,
      ...additionalParams
    }
  });
};

export const trackGameStart = async (sessionId: string): Promise<void> => {
  await sendGA4Event({
    name: 'game_start',
    params: {
      game_session_id: sessionId,
      timestamp: new Date().toISOString()
    }
  });
};

export const trackImageUpload = async (
  sessionId: string,
  imageCount: number,
  totalSize: number
): Promise<void> => {
  await sendGA4Event({
    name: 'image_upload',
    params: {
      game_session_id: sessionId,
      image_count: imageCount,
      total_size_bytes: totalSize,
      screen_name: 'ImageUploadScreen'
    }
  });
};

export const trackSurveyResponse = async (
  sessionId: string,
  questionId: string,
  questionText: string,
  answerType: string
): Promise<void> => {
  await sendGA4Event({
    name: 'survey_response',
    params: {
      game_session_id: sessionId,
      question_id: questionId,
      question_text: questionText,
      answer_type: answerType,
      screen_name: 'SurveyScreen'
    }
  });
};

export const trackSurveyComplete = async (
  sessionId: string,
  questionCount: number,
  completionTime: number
): Promise<void> => {
  await sendGA4Event({
    name: 'survey_complete',
    params: {
      game_session_id: sessionId,
      question_count: questionCount,
      completion_time_seconds: completionTime,
      screen_name: 'SurveyScreen'
    }
  });
};

export const trackGameComplete = async (
  sessionId: string,
  score: number,
  category: string,
  completionTime: number
): Promise<void> => {
  await sendGA4Event({
    name: 'game_complete',
    params: {
      game_session_id: sessionId,
      score: score,
      category: category,
      completion_time_seconds: completionTime,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackShare = async (
  sessionId: string,
  shareType: string,
  shareMethod: string
): Promise<void> => {
  await sendGA4Event({
    name: 'share',
    params: {
      game_session_id: sessionId,
      content_type: shareType,
      method: shareMethod,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackGameRestart = async (
  oldSessionId: string,
  newSessionId: string
): Promise<void> => {
  await sendGA4Event({
    name: 'game_restart',
    params: {
      old_session_id: oldSessionId,
      new_session_id: newSessionId,
      screen_name: 'ResultsScreen'
    }
  });
};

export const trackError = async (
  errorType: string,
  errorMessage: string,
  screenName: string
): Promise<void> => {
  await sendGA4Event({
    name: 'error',
    params: {
      error_type: errorType,
      error_message: errorMessage,
      screen_name: screenName
    }
  });
};

// Custom event tracking for any other events
export const trackCustomEvent = async (
  eventName: string,
  params: Record<string, any>
): Promise<void> => {
  await sendGA4Event({
    name: eventName,
    params
  });
};

// Initialize GA4 tracking
export const initGA4 = (): void => {
  console.log('Initializing GA4 tracking...', {
    isZaloMiniProgram: TRACKING_CONFIG.isZaloMiniProgram,
    isDevelopment: TRACKING_CONFIG.isDevelopment,
    measurementId: MEASUREMENT_ID
  });
  
  // Initialize gtag.js immediately for both web and Zalo environments
  if (TRACKING_CONFIG.useGtag) {
    initializeGtag();
  }
  
  // Small delay to ensure gtag is loaded before tracking
  setTimeout(() => {
    // Track initial page view
    trackScreenView('App Launch', TRACKING_CONFIG.isZaloMiniProgram ? 'ZaloMiniProgram' : 'WebApp');
  }, 100);
  
  // Set up page visibility tracking
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'visible') {
      trackCustomEvent('app_foreground', {
        timestamp: new Date().toISOString()
      });
    } else {
      trackCustomEvent('app_background', {
        timestamp: new Date().toISOString()
      });
    }
  });
  
  // Process any queued events on page unload
  window.addEventListener('beforeunload', () => {
    if (eventQueue.length > 0) {
      processBatchEvents();
    }
  });
  
  // Add global error tracking
  window.addEventListener('error', (event) => {
    trackError('javascript_error', event.message, 'global');
  });
  
  // Add unhandled promise rejection tracking
  window.addEventListener('unhandledrejection', (event) => {
    trackError('unhandled_promise_rejection', event.reason?.toString() || 'Unknown', 'global');
  });
};